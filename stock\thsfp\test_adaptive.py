#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自适应表格处理器
"""

import logging
from pathlib import Path
from adaptive_table_processor import AdaptiveTableProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_adaptive_processor():
    """测试自适应表格处理器"""
    try:
        logger.info("🚀 开始测试自适应表格处理器")
        
        # 初始化处理器
        processor = AdaptiveTableProcessor()
        
        # 查找测试图片
        current_dir = Path(__file__).parent
        images_dir = current_dir / "images"
        
        if not images_dir.exists():
            logger.error(f"图像目录不存在: {images_dir}")
            return False
        
        # 查找图片文件
        image_files = list(images_dir.glob("*.png")) + list(images_dir.glob("*.jpg")) + list(images_dir.glob("*.jpeg"))
        
        if not image_files:
            logger.error(f"在 {images_dir} 目录中未找到图片文件")
            return False
        
        # 使用第一个图片文件进行测试
        test_image = image_files[0]
        logger.info(f"测试图片: {test_image}")
        
        # 输出路径
        output_dir = current_dir / "output"
        output_dir.mkdir(exist_ok=True)
        output_excel = output_dir / f"{test_image.stem}_test_result.xlsx"
        
        logger.info("="*60)
        logger.info("🚀 开始自适应表格处理完整工作流程")
        logger.info("="*60)
        
        # 执行完整工作流程
        success = processor.process_complete_workflow(test_image, output_excel)
        
        if success:
            logger.info("✅ 测试成功！")
            logger.info(f"📄 输出文件: {output_excel}")
            return True
        else:
            logger.error("❌ 测试失败")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    test_adaptive_processor()

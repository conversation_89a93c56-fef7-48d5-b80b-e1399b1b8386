#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应表格处理器
根据图像尺寸自动计算切分块数，精准切分后使用有线表格识别，最终输出Excel
"""

import cv2
import numpy as np
from pathlib import Path
import logging
import json
import time
import pandas as pd
from bs4 import BeautifulSoup
import re
from image_splitter import ImageSplitter
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
from queue import Queue

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdaptiveTableProcessor:
    """自适应表格处理器"""
    
    def __init__(self, max_workers=4):
        self.max_dimension = 1500      # 最大处理尺寸
        self.min_scale_factor = 1.0    # 最小缩放比例改为1.0
        self.max_workers = max_workers # 最大并发线程数
        self.current_dir = Path(__file__).parent

        # 初始化标志
        self.engines_initialized = False
        self.table_cls = None
        self.wired_engine = None
        self.ocr_engine = None

        # 线程安全锁
        self.engine_lock = threading.Lock()

        logger.info("AdaptiveTableProcessor 初始化完成")
        logger.info(f"最大处理尺寸: {self.max_dimension}px")
        logger.info(f"最小缩放比例: {self.min_scale_factor}")
        logger.info(f"最大并发线程数: {self.max_workers}")
    

    
    def initialize_engines(self):
        """初始化TableStructureRec引擎"""
        if self.engines_initialized:
            return
        
        try:
            logger.info("正在初始化TableStructureRec引擎...")
            
            from table_cls import TableCls
            from wired_table_rec.main import WiredTableInput, WiredTableRecognition
            from rapidocr import RapidOCR
            
            # 初始化组件
            self.table_cls = TableCls()
            
            # 初始化有线表格识别引擎
            wired_input = WiredTableInput(model_type="unet")
            self.wired_engine = WiredTableRecognition(wired_input)
            
            # 初始化OCR引擎
            self.ocr_engine = RapidOCR()
            
            self.engines_initialized = True
            logger.info("✅ TableStructureRec引擎初始化成功")
            
        except ImportError as e:
            logger.error(f"❌ 缺少必要的库: {e}")
            raise
        except Exception as e:
            logger.error(f"❌ 引擎初始化失败: {e}")
            raise

    def create_thread_engine(self):
        """为每个线程创建独立的引擎实例"""
        try:
            from table_cls import TableCls
            from wired_table_rec.main import WiredTableInput, WiredTableRecognition
            from rapidocr import RapidOCR

            # 为线程创建独立的引擎实例
            table_cls = TableCls()
            wired_input = WiredTableInput(model_type="unet")
            wired_engine = WiredTableRecognition(wired_input)
            ocr_engine = RapidOCR()

            return {
                'table_cls': table_cls,
                'wired_engine': wired_engine,
                'ocr_engine': ocr_engine
            }
        except Exception as e:
            logger.error(f"❌ 创建线程引擎失败: {e}")
            return None

    def process_single_segment_threaded(self, segment, segment_index, engines):
        """线程安全的单个片段处理方法"""
        try:
            logger.info(f"🔄 开始处理片段 {segment_index}")
            start_time = time.time()

            # 使用线程独立的引擎
            table_cls = engines['table_cls']
            wired_engine = engines['wired_engine']
            ocr_engine = engines['ocr_engine']

            # 1. 表格分类
            is_table = table_cls(segment)
            if not is_table:
                logger.warning(f"⚠️ 片段 {segment_index} 不包含表格")
                return None

            # 2. OCR识别
            ocr_output = ocr_engine(segment)
            # 处理新版本RapidOCR的返回格式
            if hasattr(ocr_output, 'ocr_res'):
                ocr_result = ocr_output.ocr_res
            else:
                # 兼容旧版本格式
                ocr_result, _ = ocr_output if isinstance(ocr_output, tuple) else (ocr_output, None)

            if not ocr_result:
                logger.warning(f"⚠️ 片段 {segment_index} OCR识别失败")
                return None

            # 3. 表格结构识别
            html_content = wired_engine(segment, ocr_result)
            if not html_content:
                logger.warning(f"⚠️ 片段 {segment_index} 表格结构识别失败")
                return None

            # 4. 解析HTML获取统计信息
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')

            if table:
                rows = table.find_all('tr')
                cells = table.find_all(['td', 'th'])

                # 检查是否为7列表格
                if rows:
                    first_row_cells = rows[0].find_all(['td', 'th'])
                    if len(first_row_cells) == 6:
                        logger.warning(f"⚠️ 片段 {segment_index} 检测到6列，期望7列")
                    elif len(first_row_cells) == 7:
                        logger.info(f"✅ 片段 {segment_index} 正确检测到7列")

                processing_time = time.time() - start_time

                result = {
                    "segment_index": segment_index,
                    "html_content": html_content,
                    "row_count": len(rows),
                    "cell_count": len(cells),
                    "ocr_text_count": len(ocr_result),
                    "processing_time": processing_time
                }

                logger.info(f"✅ 片段 {segment_index} 处理完成: {len(rows)} 行 x {len(first_row_cells) if rows else 0} 列, 耗时: {processing_time:.2f}s")
                return result
            else:
                logger.warning(f"⚠️ 片段 {segment_index} 未找到表格元素")
                return None

        except Exception as e:
            logger.error(f"❌ 片段 {segment_index} 处理失败: {e}")
            return None



    

    

    

    
    def split_image_adaptively(self, image_path):
        """使用新的ImageSplitter进行自适应切分"""
        try:
            logger.info(f"开始使用新的ImageSplitter切分图像: {image_path}")

            # 创建临时输出目录
            temp_output_dir = Path("temp_split") / Path(image_path).stem

            # 使用新的ImageSplitter
            splitter = ImageSplitter(str(image_path), str(temp_output_dir))

            # 执行改进的切分，限制最大片段高度为1500px
            split_files = splitter.process_improved(visualize=False, max_segment_height=1500)

            # 读取切分后的图片片段
            segments = []
            for split_file in split_files:
                segment = cv2.imread(split_file)
                if segment is not None:
                    segments.append(segment)
                    height, width = segment.shape[:2]
                    logger.info(f"加载片段: {Path(split_file).name} - 尺寸: {width}x{height}")
                else:
                    logger.warning(f"无法加载切分片段: {split_file}")

            logger.info(f"总共加载了 {len(segments)} 个切分片段")
            return segments

        except Exception as e:
            logger.error(f"使用ImageSplitter切分失败: {e}")
            return []
    


    def preprocess_image(self, image_path):
        """图像预处理 - 分辨率优化"""
        try:
            if isinstance(image_path, (str, Path)):
                image = cv2.imread(str(image_path))
                if image is None:
                    raise ValueError(f"无法读取图像: {image_path}")
            else:
                image = image_path

            original_height, original_width = image.shape[:2]
            max_dimension = max(original_height, original_width)

            if max_dimension > self.max_dimension:
                scale = self.max_dimension / max_dimension
                new_width = int(original_width * scale)
                new_height = int(original_height * scale)

                resized_image = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_LANCZOS4)
                logger.info(f"图像已缩放至: {new_width} x {new_height} (缩放比例: {scale:.3f})")

                return resized_image, scale
            else:
                logger.info("图像尺寸在限制范围内，无需缩放")
                return image, 1.0

        except Exception as e:
            logger.error(f"图像预处理失败: {e}")
            raise

    def perform_ocr(self, image):
        """执行OCR识别"""
        try:
            start_time = time.time()

            rapid_ocr_output = self.ocr_engine(image, return_word_box=True)

            if rapid_ocr_output is None or not hasattr(rapid_ocr_output, 'boxes'):
                logger.warning("OCR识别结果为空")
                return []

            ocr_result = list(zip(
                rapid_ocr_output.boxes,
                rapid_ocr_output.txts,
                rapid_ocr_output.scores
            ))

            elapsed = time.time() - start_time
            logger.info(f"OCR识别完成: 检测到 {len(ocr_result)} 个文本框 (耗时: {elapsed:.3f}s)")

            return ocr_result

        except Exception as e:
            logger.error(f"OCR识别失败: {e}")
            return []

    def recognize_table_structure(self, image, ocr_result):
        """表格结构识别 - 强制使用有线表格识别"""
        try:
            start_time = time.time()

            logger.info("使用有线表格识别引擎 (wired_table_rec_v2)")

            table_results = self.wired_engine(
                image,
                ocr_result=ocr_result,
                enhance_box_line=True,
                rotated_fix=True,
                need_ocr=True,
            )

            elapsed = time.time() - start_time
            cell_count = len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0

            logger.info(f"表格结构识别完成: 检测到 {cell_count} 个单元格 (耗时: {elapsed:.3f}s)")

            return table_results

        except Exception as e:
            logger.error(f"表格结构识别失败: {e}")
            return None

    def process_single_segment(self, segment_image, segment_index):
        """处理单个片段"""
        try:
            logger.info(f"🔄 处理片段 {segment_index}")

            # 确保引擎已初始化
            self.initialize_engines()

            # 图像预处理
            processed_image, scale_factor = self.preprocess_image(segment_image)

            # OCR识别
            ocr_result = self.perform_ocr(processed_image)

            # 表格结构识别
            table_results = self.recognize_table_structure(processed_image, ocr_result)

            if table_results is None:
                logger.error(f"片段 {segment_index} 表格结构识别失败")
                return None

            return {
                "segment_index": segment_index,
                "scale_factor": scale_factor,
                "cell_count": len(table_results.cell_bboxes) if table_results.cell_bboxes is not None else 0,
                "ocr_text_count": len(ocr_result),
                "processing_time": table_results.elapse,
                "html_content": table_results.pred_html,
                "table_results": table_results
            }

        except Exception as e:
            logger.error(f"处理片段 {segment_index} 失败: {e}")
            return None

    def html_to_dataframe(self, html_content):
        """将HTML表格转换为DataFrame"""
        try:
            if not html_content or html_content.strip() == "":
                logger.warning("HTML内容为空")
                return pd.DataFrame()

            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')

            if table is None:
                logger.warning("HTML中未找到表格")
                return pd.DataFrame()

            # 提取表格数据
            rows = []
            for tr in table.find_all('tr'):
                row = []
                for cell in tr.find_all(['td', 'th']):
                    # 获取单元格文本，处理换行符
                    cell_text = cell.get_text(strip=True)
                    cell_text = re.sub(r'\s+', ' ', cell_text)  # 合并多个空白字符
                    row.append(cell_text)
                if row:  # 只添加非空行
                    rows.append(row)

            if not rows:
                logger.warning("表格中没有数据行")
                return pd.DataFrame()

            # 确保所有行的列数一致
            max_cols = max(len(row) for row in rows) if rows else 0
            for row in rows:
                while len(row) < max_cols:
                    row.append("")

            # 创建DataFrame
            df = pd.DataFrame(rows)

            logger.info(f"成功转换HTML表格: {len(df)} 行 x {len(df.columns)} 列")
            return df

        except Exception as e:
            logger.error(f"HTML转DataFrame失败: {e}")
            return pd.DataFrame()

    def merge_dataframes(self, dataframes):
        """合并多个DataFrame"""
        try:
            if not dataframes:
                logger.warning("没有DataFrame需要合并")
                return pd.DataFrame()

            # 过滤空的DataFrame
            valid_dfs = [df for df in dataframes if not df.empty]

            if not valid_dfs:
                logger.warning("所有DataFrame都为空")
                return pd.DataFrame()

            # 确保所有DataFrame的列数一致
            max_cols = max(len(df.columns) for df in valid_dfs)

            for df in valid_dfs:
                while len(df.columns) < max_cols:
                    df[len(df.columns)] = ""

            # 合并DataFrame
            merged_df = pd.concat(valid_dfs, ignore_index=True)

            logger.info(f"成功合并 {len(valid_dfs)} 个表格: 总计 {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            return merged_df

        except Exception as e:
            logger.error(f"合并DataFrame失败: {e}")
            return pd.DataFrame()

    def save_to_excel(self, dataframe, output_path):
        """保存DataFrame到Excel"""
        try:
            if dataframe.empty:
                logger.warning("DataFrame为空，无法保存到Excel")
                return False

            # 创建输出目录
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            # 保存到Excel
            with pd.ExcelWriter(str(output_file), engine='openpyxl') as writer:
                dataframe.to_excel(writer, sheet_name='表格数据', index=False, header=False)

                # 获取工作表并设置格式
                worksheet = writer.sheets['表格数据']

                # 自动调整列宽
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter

                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass

                    adjusted_width = min(max_length + 2, 50)  # 限制最大宽度
                    worksheet.column_dimensions[column_letter].width = adjusted_width

            logger.info(f"✅ Excel文件已保存: {output_file}")
            logger.info(f"📊 数据规模: {len(dataframe)} 行 x {len(dataframe.columns)} 列")

            return True

        except Exception as e:
            logger.error(f"保存Excel失败: {e}")
            return False

    def process_complete_workflow(self, image_path, output_excel_path):
        """完整的工作流程：切分 -> 识别 -> 合并 -> 输出Excel"""
        try:
            logger.info("="*60)
            logger.info("🚀 开始自适应表格处理完整工作流程")
            logger.info("="*60)

            start_time = time.time()

            # 第1步：自适应切分图像
            logger.info("📋 第1步：自适应切分图像")
            segments = self.split_image_adaptively(image_path)

            if not segments:
                logger.error("❌ 图像切分失败")
                return False

            # 第2步：多线程处理每个片段
            logger.info(f"📋 第2步：使用 {self.max_workers} 个线程并行处理 {len(segments)} 个片段")
            segment_results = []
            dataframes = []

            # 使用线程池并行处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 为每个任务创建独立的引擎实例
                future_to_segment = {}

                for i, segment in enumerate(segments, 1):
                    # 为每个线程创建独立的引擎
                    engines = self.create_thread_engine()
                    if engines:
                        future = executor.submit(self.process_single_segment_threaded, segment, i, engines)
                        future_to_segment[future] = i
                    else:
                        logger.error(f"❌ 无法为片段 {i} 创建引擎")

                # 收集结果
                completed_count = 0
                for future in as_completed(future_to_segment):
                    segment_index = future_to_segment[future]
                    try:
                        result = future.result()
                        completed_count += 1

                        if result:
                            segment_results.append(result)

                            # 转换HTML为DataFrame
                            df = self.html_to_dataframe(result["html_content"])
                            if not df.empty:
                                dataframes.append(df)
                                logger.info(f"✅ 片段 {segment_index} 处理成功: {result['cell_count']} 个单元格 ({completed_count}/{len(segments)})")
                            else:
                                logger.warning(f"⚠️ 片段 {segment_index} 转换DataFrame失败")
                        else:
                            logger.error(f"❌ 片段 {segment_index} 处理失败")

                    except Exception as e:
                        logger.error(f"❌ 片段 {segment_index} 处理异常: {e}")

            logger.info(f"🎯 多线程处理完成，成功处理 {len(dataframes)}/{len(segments)} 个片段")

            if not dataframes:
                logger.error("❌ 没有成功处理的片段")
                return False

            # 第3步：合并所有DataFrame
            logger.info("📋 第3步：合并表格数据")
            merged_df = self.merge_dataframes(dataframes)

            if merged_df.empty:
                logger.error("❌ 合并DataFrame失败")
                return False

            # 第4步：保存到Excel
            logger.info("📋 第4步：保存到Excel")
            success = self.save_to_excel(merged_df, output_excel_path)

            if not success:
                logger.error("❌ 保存Excel失败")
                return False

            # 统计信息
            total_time = time.time() - start_time
            total_cells = sum(r["cell_count"] for r in segment_results)
            total_ocr_texts = sum(r["ocr_text_count"] for r in segment_results)

            logger.info("="*60)
            logger.info("🎉 自适应表格处理完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   切分片段数: {len(segments)}")
            logger.info(f"   成功处理片段: {len(segment_results)}")
            logger.info(f"   总单元格数: {total_cells}")
            logger.info(f"   总OCR文本框: {total_ocr_texts}")
            logger.info(f"   最终表格规模: {len(merged_df)} 行 x {len(merged_df.columns)} 列")
            logger.info(f"   总处理时间: {total_time:.3f}秒")
            logger.info(f"   Excel文件: {output_excel_path}")
            logger.info("="*60)

            return True

        except Exception as e:
            logger.error(f"完整工作流程失败: {e}")
            return False



def main():
    """主函数 - 自动处理最新日期的图片"""
    try:
        logger.info("🚀 开始自动处理最新日期的图片")

        # 自动处理最新日期的图片
        success = process_latest_images()

        if success:
            logger.info("🎉 自动处理完成！")
        else:
            logger.error("❌ 自动处理失败")

    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

def get_latest_images(images_dir="images"):
    """获取images目录中最新日期的图片"""
    import re

    images_path = Path(images_dir)
    if not images_path.exists():
        logger.error(f"图片目录不存在: {images_dir}")
        return []

    # 查找所有图片文件
    image_files = []
    for ext in ['*.png', '*.jpg', '*.jpeg']:
        image_files.extend(images_path.glob(ext))

    if not image_files:
        logger.warning(f"在 {images_dir} 目录中未找到图片文件")
        return []

    # 按日期分组
    date_groups = {}
    date_pattern = re.compile(r'(\d{4}-\d{2}-\d{2})')

    for img_file in image_files:
        match = date_pattern.search(img_file.name)
        if match:
            date_str = match.group(1)
            if date_str not in date_groups:
                date_groups[date_str] = []
            date_groups[date_str].append(str(img_file))

    if not date_groups:
        logger.warning("未找到包含日期格式的图片文件")
        return []

    # 找到最新日期
    latest_date = max(date_groups.keys())
    latest_images = sorted(date_groups[latest_date])

    logger.info(f"找到最新日期: {latest_date}")
    logger.info(f"最新日期的图片: {latest_images}")

    return latest_images

def process_latest_images(images_dir="images", output_dir="output"):
    """自动处理最新日期的图片"""
    try:
        processor = AdaptiveTableProcessor()

        # 获取最新日期的图片
        latest_images = get_latest_images(images_dir)

        if not latest_images:
            logger.error("未找到可处理的图片")
            return False

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)

        success_count = 0

        for image_path in latest_images:
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"处理图片: {image_path}")
                logger.info(f"{'='*50}")

                # 生成输出文件名
                image_name = Path(image_path).stem
                output_excel = output_path / f"{image_name}_processed.xlsx"

                # 执行完整工作流程
                success = processor.process_complete_workflow(image_path, output_excel)

                if success:
                    logger.info(f"✅ 图片 {image_path} 处理成功!")
                    logger.info(f"   输出文件: {output_excel}")
                    success_count += 1
                else:
                    logger.error(f"❌ 图片 {image_path} 处理失败")

            except Exception as e:
                logger.error(f"处理图片 {image_path} 时出错: {e}")

        logger.info(f"\n🎉 批量处理完成! 成功处理 {success_count}/{len(latest_images)} 张图片")
        return success_count > 0

    except Exception as e:
        logger.error(f"批量处理失败: {e}")
        return False

def process_image(image_path, output_path=None):
    """处理指定图像的函数接口"""
    try:
        processor = AdaptiveTableProcessor()

        image_path = Path(image_path)
        if not image_path.exists():
            logger.error(f"图像文件不存在: {image_path}")
            return False

        # 如果没有指定输出路径，使用默认路径
        if output_path is None:
            output_dir = processor.current_dir / "output"
            image_name = image_path.stem
            output_path = output_dir / f"{image_name}.xlsx"

        return processor.process_complete_workflow(image_path, output_path)

    except Exception as e:
        logger.error(f"处理图像失败: {e}")
        return False

if __name__ == "__main__":
    main()

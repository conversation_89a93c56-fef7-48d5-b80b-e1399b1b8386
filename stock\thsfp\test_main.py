#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主函数
"""

import logging
from pathlib import Path
from adaptive_table_processor import main as adaptive_main

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_main():
    """测试主函数"""
    try:
        logger.info("🚀 开始测试adaptive_table_processor的main函数")
        
        # 直接调用adaptive_table_processor的main函数
        adaptive_main()
        
        logger.info("✅ 测试完成！")
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_main()

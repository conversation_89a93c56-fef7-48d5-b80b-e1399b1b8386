#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主控制程序
整合网页爬取、图片处理和Excel处理的完整流程
"""

import logging
from pathlib import Path
import time

# 导入各个模块
from web_scraper import WebScraper
from adaptive_table_processor import process_image
from excel_processor import ExcelProcessor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MainController:
    """主控制器"""
    
    def __init__(self):
        self.current_dir = Path(__file__).parent
        self.images_dir = self.current_dir / "images"
        self.output_dir = self.current_dir / "output"
        
        logger.info("MainController 初始化完成")
    
    def run_complete_workflow(self):
        """运行完整的工作流程"""
        try:
            logger.info("="*80)
            logger.info("🚀 开始完整的数据处理工作流程")
            logger.info("="*80)
            
            start_time = time.time()
            
            # 第1步：网页爬取
            logger.info("📋 第1步：爬取涨停复盘网页图片")
            scraper = WebScraper()
            scraped_images = scraper.scrape_all_articles()
            
            if not scraped_images:
                logger.error("❌ 网页爬取失败，无法继续")
                return False
            
            logger.info(f"✅ 爬取完成，获得 {len(scraped_images)} 张图片")
            
            # 第2步：图片处理
            logger.info("📋 第2步：处理图片并生成Excel")
            processed_count = 0
            
            for image_path in scraped_images:
                logger.info(f"处理图片: {Path(image_path).name}")
                
                # 生成输出路径
                image_name = Path(image_path).stem
                output_path = self.output_dir / f"{image_name}.xlsx"
                
                # 处理图片
                success = process_image(image_path, output_path)
                
                if success:
                    processed_count += 1
                    logger.info(f"✅ 图片处理成功: {output_path}")
                else:
                    logger.error(f"❌ 图片处理失败: {image_path}")
            
            if processed_count == 0:
                logger.error("❌ 所有图片处理失败，无法继续")
                return False
            
            logger.info(f"✅ 图片处理完成，成功处理 {processed_count}/{len(scraped_images)} 张图片")
            
            # 第3步：Excel处理
            logger.info("📋 第3步：处理Excel文件")
            excel_processor = ExcelProcessor()
            processed_files = excel_processor.batch_process_excel_files(self.output_dir)
            
            if not processed_files:
                logger.error("❌ Excel处理失败")
                return False
            
            logger.info(f"✅ Excel处理完成，处理 {len(processed_files)} 个文件")
            
            # 统计信息
            total_time = time.time() - start_time
            
            logger.info("="*80)
            logger.info("🎉 完整工作流程执行完成！")
            logger.info(f"📊 处理统计:")
            logger.info(f"   爬取图片数: {len(scraped_images)}")
            logger.info(f"   成功处理图片: {processed_count}")
            logger.info(f"   生成Excel文件: {len(processed_files)}")
            logger.info(f"   总处理时间: {total_time:.1f}秒")
            logger.info("📁 输出文件:")
            for file_path in processed_files:
                logger.info(f"   📄 {file_path}")
            logger.info("="*80)
            
            return True
            
        except Exception as e:
            logger.error(f"完整工作流程执行失败: {e}")
            return False

def main():
    """主函数"""
    try:
        controller = MainController()
        success = controller.run_complete_workflow()
        
        if success:
            logger.info("🎉 所有任务执行成功！")
        else:
            logger.error("❌ 任务执行失败")
        
    except Exception as e:
        logger.error(f"主程序执行失败: {e}")

if __name__ == "__main__":
    main()

# 涨停复盘表格处理系统

这是一个自动化的表格处理系统，用于处理涨停复盘相关的图片，提取表格数据并生成Excel文件。

## 功能特点

- **自适应图像切分**：根据图像尺寸自动计算最优切分块数，基于粗横线进行精准切分
- **智能表格识别**：使用有线表格识别技术，支持复杂表格结构
- **OCR文字识别**：使用RapidOCR进行高精度文字识别
- **Excel输出**：自动生成格式化的Excel文件，支持列宽自适应
- **完整工作流程**：从图片输入到Excel输出的一站式处理

## 核心算法

### 自适应切分算法
1. **尺寸分析**：根据图像尺寸和最大处理尺寸计算最优切分块数
2. **线条检测**：使用形态学操作和霍夫变换检测粗横线
3. **智能切分**：结合理想切分位置和实际线条位置进行精准切分
4. **缩放优化**：确保每个片段的缩放比例满足识别要求

## 文件结构

```
thsfp/
├── adaptive_table_processor.py  # 自适应表格处理器（核心模块）
├── web_scraper.py              # 网页爬取模块
├── excel_processor.py          # Excel处理模块
├── main_controller.py          # 主控制器
├── test_adaptive.py            # 测试脚本
├── test_main.py               # 主函数测试脚本
├── images/                    # 图片存储目录
├── output/                    # 输出Excel文件目录
├── temp_split/               # 临时切分图片目录
└── README.md                 # 说明文档
```

### 核心模块说明

1. **adaptive_table_processor.py** - 自适应表格处理器（核心）
   - 根据图像尺寸自动计算最优切分块数
   - 基于粗横线检测进行精准切分
   - 使用有线表格识别引擎（wired_table_rec_v2）
   - 自动合并多个片段并输出Excel文件

2. **main_controller.py** - 主控制程序
   - 整合所有处理步骤的完整工作流程
   - 协调各个模块的执行顺序
   - 提供统一的入口点

3. **web_scraper.py** - 网页爬取器
   - 从指定网站获取涨停复盘文章
   - 自动识别并下载最大的图片（主图）
   - 从网页内容中提取日期信息
   - 按日期格式（YYYY-MM-DD）命名保存图片

4. **excel_processor.py** - Excel处理器
   - 检测股票名称和6位数代码的混合单元格
   - 自动分离股票名称和代码到不同列
   - 将单格内容移动到第一列
   - 自动调整Excel格式和列宽

## 依赖库

```bash
pip install opencv-python numpy pandas openpyxl beautifulsoup4 requests Pillow
pip install rapidocr-onnxruntime table-cls wired-table-rec
```

## 使用方法

### 1. 直接运行主程序（推荐）

```bash
python adaptive_table_processor.py
```

### 2. 使用测试脚本

```bash
python test_adaptive.py
```

### 3. 编程调用

```python
from adaptive_table_processor import AdaptiveTableProcessor

# 初始化处理器
processor = AdaptiveTableProcessor()

# 处理图片
success = processor.process_complete_workflow(
    image_path="images/your_image.png",
    output_excel_path="output/result.xlsx"
)
```

### 4. 完整工作流程（包含网页爬取）

```bash
python main_controller.py
```

这将执行完整的工作流程：

1. 爬取涨停复盘网页图片
2. 处理图片生成Excel文件
3. 整理Excel数据格式

## 技术参数

| 参数 | 值 | 说明 |
|------|----|----|
| 最大处理尺寸 | 1500px | 表格识别引擎最佳处理尺寸 |
| 最小缩放比例 | 1.0 | 确保图片质量的最小缩放比例 |
| 识别引擎 | wired_table_rec_v2 | 有线表格识别，精度最高 |
| OCR引擎 | RapidOCR | 高速文字识别引擎 |

## 输出文件结构

```
stock/thsfp/
├── images/                    # 爬取的图片
│   ├── 2025-08-12.png
│   └── 2025-08-13.png
├── output/                    # 处理结果
│   ├── 2025-08-12.xlsx       # 原始识别结果
│   ├── 2025-08-12_processed.xlsx  # 处理后的数据
│   └── ...
└── [程序文件]
```

## 处理流程详解

### 1. 网页爬取阶段
- 访问指定网站获取文章列表
- 筛选包含"涨停复盘"的文章
- 进入文章页面分析所有图片
- 下载尺寸最大的图片作为主图
- 提取文章日期并用于文件命名

### 2. 图片处理阶段
- 分析图片尺寸，自动计算切分块数
- 检测粗横线位置，精准切分表格
- 对每个片段进行OCR和表格结构识别
- 合并所有片段的识别结果
- 输出格式化的Excel文件

### 3. Excel处理阶段
- 检测混合的股票名称和代码单元格
- 使用正则表达式分离名称和6位数代码
- 将单独的内容移动到第一列
- 自动调整列宽和格式

## 注意事项

- 确保网络连接稳定，用于网页爬取
- 图片应包含清晰的表格线条结构
- 股票代码必须为6位数字才会被识别
- 处理大量图片时请注意磁盘空间

## 更新日志

- **v3.0.0**：完整工作流程系统
  - 集成网页爬取功能
  - 自适应表格处理
  - Excel数据整理
  - 主控制程序

- **v2.0.0**：自适应表格处理器
  - 智能切分算法
  - 精准线条检测
  - 有线表格识别

- **v1.0.0**：基础表格识别功能
